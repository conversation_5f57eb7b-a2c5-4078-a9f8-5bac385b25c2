<template>
  <div class="h-full border border-surface-300 rounded-xl p-4">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 h-full min-h-0">
        <!-- Basic Information Section (Left Column) -->
        <div class="lg:col-span-1">
          <Panel header="Basic Information" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-info-circle" size="small" class="bg-blue-100 text-blue-600" />
                <span class="font-semibold">Basic Information</span>
              </div>
            </template>
            <div class="space-y-3">
              <div class="space-y-2">
                <label for="itemType" class="block text-sm font-semibold text-surface-700">Item Type</label>
                <Select
                  id="itemType"
                  v-model="item.type"
                  :options="itemTypes"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select Item Type"
                  class="w-full"
                />
              </div>
              <div class="space-y-2">
                <label for="itemTitle" class="block text-sm font-semibold text-surface-700">Title</label>
                <InputText
                  id="itemTitle"
                  v-model="item.title"
                  placeholder="Enter item title"
                  class="w-full"
                />
              </div>
            </div>
          </Panel>
        </div>

        <!-- Content Section (Right Column) -->
        <div class="lg:col-span-1 space-y-3">
          <!-- Question Type Content -->
          <Panel v-if="item.type === 'question'" header="Question Details" class="!rounded-xl h-full">
          <template #header>
            <div class="flex items-center gap-2">
              <Avatar icon="pi pi-question-circle" size="small" class="bg-orange-100 text-orange-600" />
              <span class="font-semibold">Question Details</span>
            </div>
          </template>
            <div class="space-y-4">
              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Question Text</label>
                <InputText
                  v-model="item.question"
                  placeholder="Enter your question"
                  class="w-full"
                />
              </div>

              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <label class="block text-sm font-semibold text-surface-700">Answer Options</label>
                  <Button
                    label="Add Option"
                    icon="pi pi-plus"
                    size="small"
                    outlined
                    @click="addOption"
                    class="transition-all duration-200"
                  />
                </div>
                <div class="space-y-2 max-h-40 overflow-y-auto">
                  <div v-for="(option, index) in item.options" :key="index"
                       class="flex items-center gap-3 p-3 border border-surface-200 rounded-lg hover:border-primary-300 transition-colors">
                    <RadioButton
                      :value="index"
                      v-model="item.correctAnswer"
                      :inputId="`option-${index}`"
                    />
                    <InputText
                      v-model="option.text"
                      placeholder="Enter option text"
                      class="flex-1"
                    />
                    <Chip
                      v-if="index === item.correctAnswer"
                      label="Correct"
                      icon="pi pi-check"
                      class="bg-green-100 text-green-800 border-green-200"
                    />
                    <Button
                      v-if="item.options && item.options.length > 2"
                      icon="pi pi-trash"
                      size="small"
                      severity="danger"
                      outlined
                      @click="removeOption(index)"
                      class="transition-all duration-200"
                    />
                  </div>
                </div>
              </div>
            </div>
          </Panel>

          <!-- Text Type Content -->
          <Panel v-else-if="item.type === 'text'" header="Text Content" class="border-0 shadow-none">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-file-edit" size="small" class="bg-blue-100 text-blue-600" />
                <span class="font-semibold">Text Content</span>
              </div>
            </template>
            <div class="space-y-2">
              <label class="block text-sm font-semibold text-surface-700">Content</label>
              <Textarea
                v-model="item.content"
                placeholder="Enter your text content"
                rows="4"
                class="w-full resize-none"
              />
            </div>
          </Panel>

          <!-- Image Type Content -->
          <Panel v-else-if="item.type === 'image'" header="Image Upload" class="border-0 shadow-none">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-image" size="small" class="bg-purple-100 text-purple-600" />
                <span class="font-semibold">Image Upload</span>
              </div>
            </template>
            <div class="space-y-3">
              <div class="border-2 border-dashed border-surface-300 rounded-lg p-6 text-center hover:border-primary-300 transition-colors">
                <Avatar icon="pi pi-cloud-upload" size="large" class="mb-3 bg-surface-100 text-surface-400" />
                <FileUpload mode="basic" accept="image/*" disabled class="mb-2" />
                <p class="text-sm text-surface-500 mt-2">Image upload preview only (no backend)</p>
              </div>
            </div>
          </Panel>

          <!-- Link Type Content -->
          <Panel v-else-if="item.type === 'link'" header="Link Details" class="border-0 shadow-none">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-link" size="small" class="bg-green-100 text-green-600" />
                <span class="font-semibold">Link Details</span>
              </div>
            </template>
            <div class="space-y-2">
              <label class="block text-sm font-semibold text-surface-700">URL</label>
              <InputText
                v-model="item.url"
                placeholder="https://example.com"
                class="w-full"
              />
            </div>
          </Panel>

          <!-- Map Type Content -->
          <Panel v-else-if="item.type === 'map'" header="Map Upload" class="border-0 shadow-none">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-map" size="small" class="bg-teal-100 text-teal-600" />
                <span class="font-semibold">Map Upload</span>
              </div>
            </template>
            <div class="border-2 border-dashed border-surface-300 rounded-lg p-6 text-center hover:border-primary-300 transition-colors">
              <Avatar icon="pi pi-map-marker" size="large" class="mb-3 bg-surface-100 text-surface-400" />
              <FileUpload mode="basic" accept="image/*" disabled class="mb-2" />
              <p class="text-sm text-surface-500 mt-2">Map image upload preview only (no backend)</p>
            </div>
          </Panel>

          <!-- Diagram Type Content -->
          <Panel v-else-if="item.type === 'diagram'" header="Diagram Upload" class="border-0 shadow-none">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-chart-line" size="small" class="bg-indigo-100 text-indigo-600" />
                <span class="font-semibold">Diagram Upload</span>
              </div>
            </template>
            <div class="border-2 border-dashed border-surface-300 rounded-lg p-6 text-center hover:border-primary-300 transition-colors">
              <Avatar icon="pi pi-chart-bar" size="large" class="mb-3 bg-surface-100 text-surface-400" />
              <FileUpload mode="basic" accept="image/*" disabled class="mb-2" />
              <p class="text-sm text-surface-500 mt-2">Diagram image upload preview only (no backend)</p>
            </div>
          </Panel>

          <!-- Timed Question Type Content -->
          <Panel v-else-if="item.type === 'timed-question'" header="Timed Question" class="border-0 shadow-none">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-clock" size="small" class="bg-amber-100 text-amber-600" />
                <span class="font-semibold">Timed Question</span>
              </div>
            </template>
            <div class="space-y-4">
              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Question</label>
                <InputText
                  v-model="item.question"
                  placeholder="Enter your open-ended question"
                  class="w-full"
                />
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Predefined Answer</label>
                <Textarea
                  v-model="item.timedAnswer"
                  placeholder="Enter the answer that will be revealed after the timer"
                  rows="3"
                  class="w-full resize-none"
                />
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Reveal Time (seconds)</label>
                <InputText
                  v-model="item.revealTimeSeconds"
                  type="number"
                  placeholder="30"
                  min="5"
                  max="300"
                  class="w-full"
                />
                <small class="text-surface-500">Time before the answer is revealed (5-300 seconds)</small>
              </div>
            </div>
          </Panel>
        </div>
      </div>
      <Divider />
      <!-- Save Button Section -->
        <div class="flex justify-end">
          <Button
            label="Save Item"
            icon="pi pi-save"
            @click="saveItem"
            :disabled="!item.type || !item.title"
            class="px-6 py-2 transition-all duration-200"
          />
        </div>

      <!-- Success Message -->
      <div v-if="lastCreatedItem" class="mt-4 border border-green-200 bg-green-50 rounded-xl p-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <Avatar
              icon="pi pi-check-circle"
              size="small"
              class="bg-green-100 text-green-600"
            />
            <div>
              <h3 class="font-semibold text-green-800">Item Created Successfully!</h3>
              <p class="text-sm text-green-700">"{{ lastCreatedItem.title }}" - Select from the tree to view QR code</p>
            </div>
          </div>
          <Button
            icon="pi pi-times"
            size="small"
            text
            @click="lastCreatedItem = null"
            class="text-green-600 hover:bg-green-100"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useMainStore } from '../../stores/main';
import type { Item } from '../../types/item';
import Select  from 'primevue/select';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import FileUpload from 'primevue/fileupload';
import RadioButton from 'primevue/radiobutton';
import Button from 'primevue/button';
import Panel from 'primevue/panel';
import Avatar from 'primevue/avatar';
import Chip from 'primevue/chip';
import Divider from 'primevue/divider';

interface Props {
  chapterId?: string;
  onItemCreated?: () => void;
}

const props = defineProps<Props>();
const store = useMainStore();
const route = useRoute();
const item = ref<Partial<Item>>({
  type: undefined,
  title: '',
  options: [],
  correctAnswer: undefined,
  timedAnswer: '',
  revealTimeSeconds: 30
});
const lastCreatedItem = ref<Item | null>(null);
const itemTypes = [
  { label: 'Question', value: 'question' },
  { label: 'Text Content', value: 'text' },
  { label: 'Image Upload', value: 'image' },
  { label: 'Link Details', value: 'link' },
  { label: 'Map Upload', value: 'map' },
  { label: 'Diagram Upload', value: 'diagram' },
  { label: 'Timed Question', value: 'timed-question' }
];

watch(() => props.chapterId, () => {
  item.value = {
    type: undefined,
    title: '',
    options: [],
    correctAnswer: undefined,
    timedAnswer: '',
    revealTimeSeconds: 30
  };
  lastCreatedItem.value = null; // Clear QR code when switching chapters
});

const addOption = () => {
  item.value.options = [...(item.value.options || []), { text: '' }];
};

const removeOption = (index: number) => {
  if (item.value.options) {
    item.value.options.splice(index, 1);
    // Adjust correct answer if needed
    if (item.value.correctAnswer !== undefined && item.value.correctAnswer >= index) {
      if (item.value.correctAnswer === index) {
        item.value.correctAnswer = undefined;
      } else {
        item.value.correctAnswer--;
      }
    }
  }
};

const saveItem = async () => {
  if (props.chapterId && item.value.type && item.value.title && route.params.id) {
    const { id, ...itemData } = { ...item.value } as Item;
    const newItem = await store.addItem(route.params.id as string, props.chapterId, itemData);
    lastCreatedItem.value = newItem;
    item.value = {
      type: undefined,
      title: '',
      options: [],
      correctAnswer: undefined,
      timedAnswer: '',
      revealTimeSeconds: 30
    };

    // Call the callback to refresh the tree data
    if (props.onItemCreated) {
      props.onItemCreated();
    }
  }
};
</script>