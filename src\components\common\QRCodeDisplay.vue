<template>
  <div class="space-y-3">
    <!-- Loading State -->
    <div v-if="loading" class="text-center border border-surface-300 rounded-xl p-4">
      <div class="py-6">
        <Avatar icon="pi pi-spin pi-spinner" size="large" class="mb-3 bg-primary-100 text-primary-600" />
        <h3 class="text-sm font-semibold text-surface-700 mb-1">Generating QR Code</h3>
        <p class="text-surface-500 text-xs">Please wait while we create your QR code...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center border border-red-200 bg-red-50 rounded-xl p-4">
      <div class="py-6">
        <Avatar icon="pi pi-exclamation-triangle" size="large" class="mb-3 bg-red-100 text-red-600" />
        <h3 class="text-sm font-semibold text-red-700 mb-1">Error Generating QR Code</h3>
        <p class="text-red-600 text-xs">{{ error }}</p>
      </div>
    </div>

    <!-- QR Code Display -->
    <div v-else-if="qrData" class="space-y-3">
      <!-- QR Code Info -->
      <div class="text-center border border-surface-300 rounded-xl p-4">
        <div class="space-y-3">
          <div>
            <h3 class="text-lg font-bold text-surface-800 mb-2">{{ qrData.item.title }}</h3>
            <div class="flex items-center justify-center gap-2">
              <Chip
                :label="qrData.data.bookTitle"
                icon="pi pi-book"
                class="bg-blue-100 text-blue-800 border-blue-200"
              />
              <Chip
                :label="qrData.data.chapterTitle"
                icon="pi pi-list"
                class="bg-green-100 text-green-800 border-green-200"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Main QR Code and Customization Layout -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-3">
        <!-- QR Code Display -->
        <div class="text-center border border-surface-300 rounded-xl p-4">
          <div class="p-2">
            <div class="flex justify-center">
              <div class="p-2 bg-white border border-surface-200 rounded-lg shadow-md">
                <img
                  :src="qrData.qrCode"
                  :alt="`QR Code for ${qrData.item.title}`"
                  class="w-32 h-32 object-contain"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- Color Customization Panel -->
        <Panel header="Customize Colors" class="shadow-sm">
          <template #header>
            <div class="flex items-center gap-2">
              <Avatar icon="pi pi-palette" size="small" class="bg-purple-100 text-purple-600" />
              <span class="font-semibold">Customize Colors</span>
            </div>
          </template>
          <div class="space-y-6">
            <!-- Pattern Color -->
            <div class="space-y-3">
              <label class="block text-sm font-semibold text-surface-700">Pattern Color</label>
              <div class="flex items-center gap-3">
                <ColorPicker
                  v-model="selectedColor"
                  format="hex"
                  @change="onColorChange"
                  class="flex-shrink-0"
                />
                <InputText
                  v-model="selectedColor"
                  placeholder="#1f2937"
                  class="flex-1"
                  @input="onColorInputChange"
                />
                <Button
                  icon="pi pi-refresh"
                  size="small"
                  outlined
                  @click="resetPatternColor"
                  v-tooltip="'Reset pattern color'"
                  class="flex-shrink-0"
                />
              </div>
            </div>

            <!-- Background Color -->
            <div class="space-y-3">
              <label class="block text-sm font-semibold text-surface-700">Background Color</label>
              <div class="flex items-center gap-3">
                <ColorPicker
                  v-model="selectedBackgroundColor"
                  format="hex"
                  @change="onBackgroundColorChange"
                  class="flex-shrink-0"
                />
                <InputText
                  v-model="selectedBackgroundColor"
                  placeholder="#ffffff"
                  class="flex-1"
                  @input="onBackgroundColorInputChange"
                />
                <Button
                  icon="pi pi-refresh"
                  size="small"
                  outlined
                  @click="resetBackgroundColor"
                  v-tooltip="'Reset background color'"
                  class="flex-shrink-0"
                />
              </div>
            </div>
          </div>
        </Panel>
      </div>

      <!-- Actions Panel -->
      <div class="border border-surface-300 rounded-xl p-4">
        <div class="space-y-4">
          <!-- Main Actions -->
          <div class="flex gap-3 justify-center flex-wrap">
            <Button
              label="Preview"
              icon="pi pi-eye"
              @click="previewContent"
              severity="success"
              class="transition-all duration-200"
            />
            <Button
              label="Download"
              icon="pi pi-download"
              @click="downloadQRCode"
              class="transition-all duration-200"
            />
            <Button
              label="Regenerate"
              icon="pi pi-refresh"
              @click="regenerateQRCode"
              severity="secondary"
              class="transition-all duration-200"
            />
          </div>

            <!-- Edit/Delete Actions -->
            <div v-if="authStore.canEdit || (onDelete && authStore.canDelete)"
                 class="flex gap-3 justify-center flex-wrap pt-4 border-t border-surface-200">
              <Button
                v-if="authStore.canEdit"
                label="Edit Item"
                icon="pi pi-pencil"
                @click="editItem"
                severity="warning"
                outlined
                class="transition-all duration-200"
              />
              <Button
                v-if="onDelete && authStore.canDelete"
                label="Delete Item"
                icon="pi pi-trash"
                @click="onDelete"
                severity="danger"
                outlined
                class="transition-all duration-200"
              />
            </div>
        </div>
      </div>

      <!-- Item Info -->
      <div class="bg-surface-50 border border-surface-200 rounded-xl p-4">
        <div class="text-center space-y-2">
          <div class="flex items-center justify-center gap-2">
            <Chip
              :label="qrData.item.type"
              icon="pi pi-tag"
              class="bg-blue-100 text-blue-800 border-blue-200"
            />
            <Chip
              :label="`ID: ${qrData.data.itemId}`"
              icon="pi pi-id-card"
              class="bg-gray-100 text-gray-800 border-gray-200"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import Button from 'primevue/button';
import ColorPicker from 'primevue/colorpicker';
import InputText from 'primevue/inputtext';
import Panel from 'primevue/panel';
import Avatar from 'primevue/avatar';
import Chip from 'primevue/chip';
import { useAuthStore } from '../../stores/auth';

interface Props {
  bookId: string;
  chapterId: string;
  itemId: string;
  onDelete?: () => void;
  onEdit?: () => void;
}

interface QRData {
  qrCode: string;
  data: {
    bookId: string;
    chapterId: string;
    itemId: string;
    bookTitle: string;
    chapterTitle: string;
    itemTitle: string;
    itemType: string;
    url: string;
  };
  item: {
    id: string;
    title: string;
    type: string;
    [key: string]: any;
  };
  styled?: boolean;
  displayUrl?: string;
}

const props = defineProps<Props>();
const authStore = useAuthStore();
const loading = ref(false);
const error = ref<string | null>(null);
const qrData = ref<QRData | null>(null);
const styled = ref(false);
const selectedColor = ref('#1f2937'); // Default surface color
const selectedBackgroundColor = ref('#ffffff'); // Default white background

const generateQRCode = async (useStyled = false, color = '#1f2937', backgroundColor = '#ffffff') => {
  loading.value = true;
  error.value = null;

  try {
    let url;
    if (useStyled) {
      url = `http://localhost:3001/api/qr-styled/${props.bookId}/${props.chapterId}/${props.itemId}?color=${encodeURIComponent(color)}&backgroundColor=${encodeURIComponent(backgroundColor)}`;
    } else {
      url = `http://localhost:3001/api/qr/${props.bookId}/${props.chapterId}/${props.itemId}`;
    }

    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to generate QR code: ${response.status}`);
    }

    const data = await response.json();
    qrData.value = data;
    styled.value = useStyled || data.styled || false;
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to generate QR code';
    console.error('Error generating QR code:', err);
  } finally {
    loading.value = false;
  }
};

const onColorChange = () => {
  // Ensure the color has # prefix when using color picker
  if (selectedColor.value && !selectedColor.value.startsWith('#')) {
    selectedColor.value = '#' + selectedColor.value;
  }
  // Color changed, but don't regenerate automatically
  // User needs to click "Regenerate" to apply changes
};

const onColorInputChange = () => {
  // Validate hex color format but don't regenerate automatically
  // User needs to click "Regenerate" to apply changes
};

const onBackgroundColorChange = () => {
  // Ensure the background color has # prefix when using color picker
  if (selectedBackgroundColor.value && !selectedBackgroundColor.value.startsWith('#')) {
    selectedBackgroundColor.value = '#' + selectedBackgroundColor.value;
  }
  // Background color changed, but don't regenerate automatically
  // User needs to click "Regenerate" to apply changes
};

const onBackgroundColorInputChange = () => {
  // Validate hex color format but don't regenerate automatically
  // User needs to click "Regenerate" to apply changes
};

const resetPatternColor = () => {
  selectedColor.value = '#1f2937';
  // Don't regenerate automatically - user needs to click "Regenerate"
};

const resetBackgroundColor = () => {
  selectedBackgroundColor.value = '#ffffff';
  // Don't regenerate automatically - user needs to click "Regenerate"
};

const regenerateQRCode = () => {
  // Always use styled version with current colors when regenerating
  generateQRCode(true, selectedColor.value, selectedBackgroundColor.value);
};

const previewContent = () => {
  if (qrData.value?.displayUrl) {
    window.open(qrData.value.displayUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
  }
};

const downloadQRCode = () => {
  if (!qrData.value) return;

  const link = document.createElement('a');
  link.href = qrData.value.qrCode;
  link.download = `qr-code-${qrData.value.data.itemTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}-800x800.png`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const editItem = () => {
  if (props.onEdit) {
    props.onEdit();
  }
};

// Watch for prop changes and regenerate QR code
watch([() => props.bookId, () => props.chapterId, () => props.itemId], () => {
  if (props.bookId && props.chapterId && props.itemId) {
    generateQRCode(true, selectedColor.value, selectedBackgroundColor.value); // Start with styled version
  }
}, { immediate: true });

onMounted(() => {
  if (props.bookId && props.chapterId && props.itemId) {
    generateQRCode(true, selectedColor.value, selectedBackgroundColor.value); // Start with styled version
  }
});
</script>

<style scoped>
.qr-code-container {
  max-width: 350px;
  padding: 0.75rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
}

/* QR code image is now styled with Tailwind classes */

/* Button container */
.qr-code-container .flex {
  flex-shrink: 0;
  margin: 0.5rem 0;
}

/* Info section */
.qr-code-container .text-xs {
  flex-shrink: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
  margin-top: 0.5rem;
}

/* Button styling improvements */
.qr-code-container :deep(.p-button) {
  transition: all 0.2s ease;
  font-size: 0.8rem;
  border-radius: 6px;
}

.qr-code-container :deep(.p-button.p-button-sm) {
  padding: 0.4rem 0.75rem;
  font-size: 0.75rem;
}

.qr-code-container :deep(.p-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.12);
}

.qr-code-container :deep(.p-button.p-button-outlined) {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .qr-code-container {
    max-width: 100%;
    margin: 0;
    border-radius: 8px;
    padding: 0.5rem;
    max-height: 70vh;
  }

  .qr-code-image img {
    max-width: 240px;
    max-height: 240px;
    width: 240px;     /* Fixed width for mobile */
    height: 240px;    /* Fixed height for mobile */
  }

  .qr-code-container :deep(.p-button) {
    font-size: 0.7rem;
  }

  .qr-code-container :deep(.p-button.p-button-sm) {
    padding: 0.3rem 0.6rem;
    font-size: 0.7rem;
  }

  .flex {
    gap: 0.375rem !important;
  }
}
</style>
